import { symlink } from "node:fs/promises";
import { dirname, resolve } from "node:path";
import { fileURLToPath } from "node:url";

const __dirname = dirname(fileURLToPath(import.meta.url));
const target = resolve(__dirname, "../../packages/ui/src/components");
const linkPath = resolve(__dirname, "../web/components/ui");

try {
  await symlink(target, linkPath, "dir");
  console.log("✅ Symlink created:", linkPath, "->", target);
} catch (err) {
  if (err.code === "EEXIST") {
    console.log("ℹ️ Symlink already exists:", linkPath);
  } else {
    console.error("❌ Error creating symlink:", err);
  }
}
