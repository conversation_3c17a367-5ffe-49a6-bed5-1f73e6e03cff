import { symlink } from "node:fs/promises";
import { resolve } from "node:path";
import { existsSync } from "node:fs";

const target = resolve("packages/ui/src/components");
const linkPath = resolve("apps/web/components/ui");

try {
  if (!existsSync(linkPath)) {
    await symlink(target, linkPath, "dir");
    console.log("✅ Symlink created:", linkPath, "->", target);
  } else {
    console.log("ℹ️ Symlink already exists:", linkPath);
  }
} catch (err) {
  console.error("❌ Error creating symlink:", err);
}
