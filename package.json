{"name": "fridayai", "version": "1.5.1", "private": true, "license": "MIT", "scripts": {"build": "turbo build", "link": "bun run ./scripts/link-ui.mjs", "dev": "turbo dev", "lint": "turbo lint", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "ui": "cd packages/ui && bun run ui"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "prettier": "^3.6.2", "turbo": "2.1.1"}, "engines": {"bun": ">=1.2.18"}, "workspaces": ["apps/*", "packages/*"], "packageManager": "bun@1.2.19"}